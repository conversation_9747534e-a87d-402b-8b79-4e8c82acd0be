"use client"

import { useState, useRef, useEffect } from "react"
import { ChevronDown, LogOut, User, Setting<PERSON>, AlertTriangle } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

interface NavItem {
  id: string
  label: string
  view: string
  ariaLabel?: string
}

interface NavigationProps {
  currentView: string
  onViewChange: (view: string) => void
  onLogout: () => void
}

export default function Navigation({ currentView, onViewChange, onLogout }: NavigationProps) {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showLogoutDialog, setShowLogoutDialog] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)

  const navItems: NavItem[] = [
    { id: "home", label: "Home", view: "dashboard", ariaLabel: "Navigate to home dashboard" },
    { id: "room-dashboard", label: "Room Dashboard", view: "dashboard", ariaLabel: "Navigate to room dashboard" },
    { id: "room-2", label: "Room 2", view: "room-data", ariaLabel: "Navigate to room 2 sensor data" },
    { id: "temperature-graph", label: "Temperature Graph", view: "temperature-graph", ariaLabel: "Navigate to temperature analytics" },
    { id: "set-threshold", label: "Set Threshold", view: "set-threshold", ariaLabel: "Navigate to threshold settings" },
    { id: "edit-device", label: "Edit Device", view: "edit-device", ariaLabel: "Navigate to device editor" },
  ]

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
    }

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserMenu])

  // Handle escape key to close menu
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showUserMenu) {
        setShowUserMenu(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [showUserMenu])

  const handleLogoutClick = () => {
    setShowUserMenu(false)
    setShowLogoutDialog(true)
  }

  const handleLogoutConfirm = () => {
    setShowLogoutDialog(false)
    onLogout()
  }

  const handleLogoutCancel = () => {
    setShowLogoutDialog(false)
  }

  return (
    <nav className="bg-white/80 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">IoT</span>
            </div>
            <span className="font-bold text-gray-800">Dashboard</span>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex space-x-1 overflow-x-auto" role="navigation" aria-label="Main navigation">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => onViewChange(item.view)}
                  aria-label={item.ariaLabel || item.label}
                  aria-current={currentView === item.view ? "page" : undefined}
                  className={`whitespace-nowrap px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === item.view
                      ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md"
                      : "text-gray-600 hover:text-gray-800 hover:bg-gray-100"
                  }`}
                >
                  {item.label}
                </button>
              ))}
            </div>

            {/* User Menu */}
            <div className="relative" ref={userMenuRef}>
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                aria-expanded={showUserMenu}
                aria-haspopup="menu"
                aria-label="User menu"
                className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" aria-hidden="true" />
                </div>
                <span className="text-sm font-medium text-gray-700 hidden sm:block">Admin</span>
                <ChevronDown
                  className={`w-4 h-4 text-gray-500 transition-transform ${showUserMenu ? "rotate-180" : ""}`}
                  aria-hidden="true"
                />
              </button>

              {/* Dropdown Menu */}
              {showUserMenu && (
                <div
                  className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50"
                  role="menu"
                  aria-label="User menu options"
                >
                  <div className="px-4 py-2 border-b border-gray-100" role="presentation">
                    <p className="text-sm font-medium text-gray-800">Admin User</p>
                    <p className="text-xs text-gray-500"><EMAIL></p>
                  </div>

                  <button
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2 focus:outline-none focus:bg-gray-50"
                    role="menuitem"
                    aria-label="Open settings"
                  >
                    <Settings className="w-4 h-4" aria-hidden="true" />
                    <span>Settings</span>
                  </button>

                  <button
                    onClick={handleLogoutClick}
                    className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 border-t border-gray-100 mt-1 focus:outline-none focus:bg-red-50"
                    role="menuitem"
                    aria-label="Logout from application"
                  >
                    <LogOut className="w-4 h-4" aria-hidden="true" />
                    <span>Logout</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Logout Confirmation Dialog */}
      <Dialog open={showLogoutDialog} onOpenChange={setShowLogoutDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Confirm Logout
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to logout? You will need to sign in again to access the dashboard.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={handleLogoutCancel}
              className="flex-1 sm:flex-none"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleLogoutConfirm}
              className="flex-1 sm:flex-none"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </nav>
  )
}
