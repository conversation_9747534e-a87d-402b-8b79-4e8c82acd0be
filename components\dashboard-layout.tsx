"use client"

import { useRouter } from "next/navigation"
import Navigation from "@/components/navigation"
import RoomDashboard from "@/components/room-dashboard"

interface DashboardLayoutProps {
  currentView: string
  onLogout: () => void
}

export default function DashboardLayout({ currentView, onLogout }: DashboardLayoutProps) {
  const router = useRouter()

  const handleViewChange = (view: string) => {
    switch (view) {
      case 'dashboard':
        router.push('/dashboard')
        break
      case 'room-data':
        router.push('/dashboard/room-data')
        break
      case 'temperature-graph':
        router.push('/dashboard/temperature-graph')
        break
      case 'set-threshold':
        router.push('/dashboard/set-threshold')
        break
      case 'edit-device':
        router.push('/dashboard/edit-device')
        break
      default:
        router.push('/dashboard')
    }
  }

  const handleRoomSelect = (roomId: number) => {
    router.push(`/dashboard/room-data?roomId=${roomId}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <Navigation
        currentView={currentView}
        onViewChange={handleViewChange}
        onLogout={onLogout}
      />
      <main className="container mx-auto px-4 py-8">
        <RoomDashboard onRoomSelect={handleRoomSelect} />
      </main>
    </div>
  )
}
