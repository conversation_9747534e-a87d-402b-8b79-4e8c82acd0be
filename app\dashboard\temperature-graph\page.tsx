"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Navigation from "@/components/navigation"
import TemperatureGraph from "@/components/temperature-graph"

export default function TemperatureGraphPage() {
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      router.push('/login')
      return
    }
    setIsAuthenticated(true)
    setIsLoading(false)
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('auth-token')
    router.push('/login')
  }

  const handleViewChange = (view: string) => {
    switch (view) {
      case 'dashboard':
        router.push('/dashboard')
        break
      case 'room-data':
        router.push('/dashboard/room-data')
        break
      case 'temperature-graph':
        router.push('/dashboard/temperature-graph')
        break
      case 'set-threshold':
        router.push('/dashboard/set-threshold')
        break
      case 'edit-device':
        router.push('/dashboard/edit-device')
        break
      default:
        router.push('/dashboard')
    }
  }

  const handleSetThreshold = () => {
    router.push('/dashboard/set-threshold')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <Navigation 
        currentView="temperature-graph" 
        onViewChange={handleViewChange} 
        onLogout={handleLogout} 
      />
      <main className="container mx-auto px-4 py-8">
        <TemperatureGraph onSetThreshold={handleSetThreshold} />
      </main>
    </div>
  )
}
