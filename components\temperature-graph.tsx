"use client"


import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js'
import { Line } from 'react-chartjs-2'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

interface TemperatureData {
  timestamp: string
  temperature: number
  humidity?: number
}

interface TemperatureGraphProps {
  onSetThreshold: () => void
}

export default function TemperatureGraph({ onSetThreshold }: TemperatureGraphProps) {
  // Mock temperature data for the past 7 days
  const temperatureData: TemperatureData[] = [
    { timestamp: '2024-01-24', temperature: 18.5, humidity: 45 },
    { timestamp: '2024-01-25', temperature: 19.2, humidity: 48 },
    { timestamp: '2024-01-26', temperature: 17.8, humidity: 52 },
    { timestamp: '2024-01-27', temperature: 20.1, humidity: 44 },
    { timestamp: '2024-01-28', temperature: 18.9, humidity: 47 },
    { timestamp: '2024-01-29', temperature: 19.5, humidity: 46 },
    { timestamp: '2024-01-30', temperature: 18.0, humidity: 49 },
  ]

  const currentTemp = temperatureData[temperatureData.length - 1]?.temperature || 18.0
  const avgTemp = temperatureData.reduce((sum, data) => sum + data.temperature, 0) / temperatureData.length

  const chartData = {
    labels: temperatureData.map(data => {
      const date = new Date(data.timestamp)
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    }),
    datasets: [
      {
        label: 'Temperature (°C)',
        data: temperatureData.map(data => data.temperature),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: 'white',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      },
    ],
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            return `Temperature: ${context.parsed.y}°C`
          }
        }
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
      },
      y: {
        beginAtZero: false,
        min: Math.min(...temperatureData.map(d => d.temperature)) - 2,
        max: Math.max(...temperatureData.map(d => d.temperature)) + 2,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        border: {
          display: false,
        },
        ticks: {
          callback: function(value: any) {
            return value + '°C'
          }
        }
      },
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
  }
  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent mb-2">
          Analytics Dashboard
        </h1>
        <p className="text-gray-600">Temperature monitoring and threshold management</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <div className="lg:col-span-3">
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-0 shadow-lg h-full">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">🌡️ Temperature</CardTitle>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => {}}
                className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white border-0 shadow-md"
                size="sm"
              >
                View Analytics
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-3">
          <Card className="bg-gradient-to-br from-emerald-50 to-teal-50 border-0 shadow-lg h-full">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800">Current Value</CardTitle>
              <p className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                {currentTemp.toFixed(1)}°C
              </p>
              <p className="text-sm text-emerald-600 font-medium">● Normal Range</p>
            </CardHeader>
          </Card>
        </div>

        <div className="lg:col-span-6">
          <Card className="bg-white border-0 shadow-lg h-full">
            <CardContent className="p-6">
              <div className="mb-4">
                <Badge className="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-blue-200 px-4 py-2">
                  <span role="img" aria-label="Chart">📈</span> Temperature (°C) - Past 7 Days
                </Badge>
              </div>

              <div className="h-64 relative" role="img" aria-label="Temperature chart showing data for the past 7 days">
                <Line data={chartData} options={chartOptions} />
              </div>

              <div className="mt-4 flex justify-between items-center text-sm text-gray-600">
                <span>Average: {avgTemp.toFixed(1)}°C</span>
                <span>Range: {Math.min(...temperatureData.map(d => d.temperature)).toFixed(1)}°C - {Math.max(...temperatureData.map(d => d.temperature)).toFixed(1)}°C</span>
              </div>

              <div className="mt-6 flex justify-center">
                <Button
                  onClick={onSetThreshold}
                  className="bg-gradient-to-r from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600 text-white px-6 py-2 rounded-lg shadow-lg font-medium focus:ring-2 focus:ring-violet-500 focus:ring-offset-2"
                  aria-label="Configure temperature threshold settings"
                >
                  <span role="img" aria-label="Settings">⚙️</span> Set Threshold Values
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
