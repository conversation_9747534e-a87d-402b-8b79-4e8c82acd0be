# 🔧 Troubleshooting Guide

## 🚨 Common Error: Path with Special Characters

### Error Message:
```
'3dweb\smart-building-frontend-student-\node_modules\.bin\' is not recognized as an internal or external command
```

### 🎯 Root Cause:
The folder path contains a special character (`&`) which causes issues with Windows command line execution.

## 🛠️ Solutions (Try in Order)

### Solution 1: Use Alternative Scripts
```bash
# Try these alternative commands:
npm run dev-alt
# or
npm run dev-safe
# or
yarn dev
```

### Solution 2: Rename the Folder (Recommended)
1. Close all terminals and VS Code
2. Rename the folder from `smart-building-frontend-student-` to `smart-building-frontend-student`
3. Remove the `&` from the parent folder name if possible
4. Reopen the project and try `npm run dev`

### Solution 3: Use PowerShell Script
```powershell
# Run the PowerShell script:
.\run-dev.ps1
```

### Solution 4: Use Batch File
```cmd
# Double-click or run:
run-dev.bat
```

### Solution 5: Manual Command
```cmd
# Navigate to the project folder and run:
node_modules\.bin\next.cmd dev
```

### Solution 6: Move Project Location
1. Copy the entire project to a path without special characters
2. Example: `C:\Projects\smart-building-dashboard\`
3. Run `npm install` in the new location
4. Try `npm run dev`

## 🔍 Diagnostic Commands

### Check if Next.js is Installed:
```bash
npm run check
```

### Reinstall Dependencies:
```bash
# Delete node_modules and package-lock.json
rm -rf node_modules package-lock.json
# or on Windows:
rmdir /s node_modules
del package-lock.json

# Reinstall
npm install
```

### Check Node.js Version:
```bash
node --version
npm --version
```

## 🎯 Alternative Development Methods

### Method 1: Use Yarn Instead of NPM
```bash
# Install yarn globally
npm install -g yarn

# Use yarn to run the project
yarn install
yarn dev
```

### Method 2: Use Different Port
```bash
npm run dev -- -p 3001
# Then open http://localhost:3001
```

### Method 3: Use VS Code Terminal
1. Open VS Code
2. Open Terminal (Ctrl + `)
3. Try running commands from VS Code's integrated terminal

## 📋 Pre-flight Checklist

Before running the development server, ensure:

- [ ] Node.js 18+ is installed
- [ ] Project folder doesn't have special characters in path
- [ ] `node_modules` folder exists
- [ ] `package.json` exists
- [ ] Internet connection is available (for first-time setup)

## 🆘 Still Having Issues?

### For Sister Ayesha:

Don't worry! These errors are completely normal when learning web development. Here's what to do:

1. **Take a Screenshot** of the error message
2. **Try each solution** one by one
3. **Ask for help** - every developer faces these issues
4. **Learn from it** - understanding these errors makes you a better developer

### Quick Fix for Most Issues:
```bash
# The nuclear option - reinstall everything:
npm run install-and-dev
```

### Contact Information:
If you're still stuck, remember that debugging is part of learning. Each error you solve makes you stronger as a developer!

## 🎓 Learning from Errors

### What This Error Teaches:
1. **File paths matter** in programming
2. **Special characters** can cause issues
3. **Multiple solutions** exist for most problems
4. **Environment setup** is crucial for development

### Skills You're Building:
- Problem-solving
- Command line usage
- Understanding development environments
- Debugging techniques

---

**Remember: Every expert was once a beginner who refused to give up! 💪**
