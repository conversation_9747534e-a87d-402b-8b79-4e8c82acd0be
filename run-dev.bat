@echo off
echo ========================================
echo IoT Smart Building Dashboard
echo ========================================
echo.
echo Attempting to start development server...
echo.

REM Try different methods to start the dev server

echo Method 1: Using npm run dev
npm run dev
if %ERRORLEVEL% EQU 0 goto success

echo.
echo Method 1 failed. Trying Method 2: Using npx next dev
npx next dev
if %ERRORLEVEL% EQU 0 goto success

echo.
echo Method 2 failed. Trying Method 3: Using full path
"%~dp0node_modules\.bin\next.cmd" dev
if %ERRORLEVEL% EQU 0 goto success

echo.
echo Method 3 failed. Trying Method 4: Using node directly
node "%~dp0node_modules\next\dist\bin\next" dev
if %ERRORLEVEL% EQU 0 goto success

echo.
echo ========================================
echo All methods failed!
echo ========================================
echo.
echo Possible solutions:
echo 1. Rename the folder to remove special characters (& symbol)
echo 2. Run: npm install
echo 3. Try: yarn dev (if you have yarn installed)
echo 4. Move the project to a path without special characters
echo.
echo Current directory: %CD%
echo.
pause
goto end

:success
echo.
echo ========================================
echo Development server started successfully!
echo Open http://localhost:3000 in your browser
echo ========================================
echo.

:end
