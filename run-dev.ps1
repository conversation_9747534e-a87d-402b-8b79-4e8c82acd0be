# IoT Smart Building Dashboard - Development Server Launcher
# PowerShell Script to handle path issues

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "IoT Smart Building Dashboard" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Attempting to start development server..." -ForegroundColor Yellow
Write-Host ""

# Get current directory
$currentDir = Get-Location

# Try different methods to start the dev server
try {
    Write-Host "Method 1: Using npm run dev" -ForegroundColor Green
    npm run dev
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success!" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "Method 1 failed: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    Write-Host ""
    Write-Host "Method 2: Using npx next dev" -ForegroundColor Green
    npx next dev
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success!" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "Method 2 failed: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    Write-Host ""
    Write-Host "Method 3: Using yarn dev" -ForegroundColor Green
    yarn dev
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success!" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "Method 3 failed: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    Write-Host ""
    Write-Host "Method 4: Using full path to next" -ForegroundColor Green
    $nextPath = Join-Path $currentDir "node_modules\.bin\next.cmd"
    & $nextPath dev
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success!" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "Method 4 failed: $($_.Exception.Message)" -ForegroundColor Red
}

# If all methods fail, show troubleshooting info
Write-Host ""
Write-Host "========================================" -ForegroundColor Red
Write-Host "All methods failed!" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red
Write-Host ""
Write-Host "Possible solutions:" -ForegroundColor Yellow
Write-Host "1. Rename the folder to remove special characters (& symbol)" -ForegroundColor White
Write-Host "2. Run: npm install" -ForegroundColor White
Write-Host "3. Try: yarn dev (if you have yarn installed)" -ForegroundColor White
Write-Host "4. Move the project to a path without special characters" -ForegroundColor White
Write-Host "5. Try running from a different terminal (Command Prompt, Git Bash)" -ForegroundColor White
Write-Host ""
Write-Host "Current directory: $currentDir" -ForegroundColor Cyan
Write-Host ""
Write-Host "For more help, check the README.md file" -ForegroundColor Yellow
Write-Host ""

# Check if node_modules exists
if (Test-Path "node_modules") {
    Write-Host "✓ node_modules folder exists" -ForegroundColor Green
} else {
    Write-Host "✗ node_modules folder missing - run 'npm install'" -ForegroundColor Red
}

# Check if package.json exists
if (Test-Path "package.json") {
    Write-Host "✓ package.json exists" -ForegroundColor Green
} else {
    Write-Host "✗ package.json missing" -ForegroundColor Red
}

# Check if next is installed
$nextExists = Test-Path "node_modules\next"
if ($nextExists) {
    Write-Host "✓ Next.js is installed" -ForegroundColor Green
} else {
    Write-Host "✗ Next.js not found - run 'npm install'" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
