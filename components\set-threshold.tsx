"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Lock, AlertCircle } from "lucide-react"

interface SetThresholdProps {
  onVerify: () => void
  onCancel: () => void
}

export default function SetThreshold({ onVerify, onCancel }: SetThresholdProps) {
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleVerify = async () => {
    if (!password.trim()) {
      setError("Password is required")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // Simulate password verification
      await new Promise(resolve => setTimeout(resolve, 1000))

      // For demo purposes, accept any non-empty password
      onVerify()
    } catch (err) {
      setError("Verification failed. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-md mx-auto">
      <Card className="bg-white border-0 shadow-2xl">
        <CardHeader className="text-center gradient-danger text-white rounded-t-lg">
          <CardTitle className="text-xl font-bold flex items-center justify-center gap-2">
            <Lock className="h-5 w-5" aria-hidden="true" />
            Level 2 Access Required
          </CardTitle>
          <p className="text-red-100 mt-2 text-sm">Please enter your level 2 password to modify threshold values</p>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="password" className="text-gray-700 font-medium">
              Security Password
            </Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value)
                if (error) setError("") // Clear error when user types
              }}
              className={`bg-gray-50 border-gray-200 focus:border-red-500 focus:ring-red-500 h-12 ${
                error ? 'border-red-500' : ''
              }`}
              placeholder="••••••••"
              aria-describedby={error ? "password-error" : undefined}
              aria-invalid={!!error}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleVerify()
                }
              }}
            />
          </div>

          <div className="space-y-3">
            <Button
              onClick={handleVerify}
              disabled={isLoading}
              className="w-full gradient-success gradient-success-hover text-white h-12 font-medium shadow-lg disabled:opacity-50 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
              aria-label="Verify password and continue to device settings"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Verifying...
                </>
              ) : (
                <>
                  <span className="mr-2">✓</span>
                  Verify and Continue
                </>
              )}
            </Button>
            <Button
              onClick={onCancel}
              variant="outline"
              disabled={isLoading}
              className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 h-12 bg-transparent focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              aria-label="Cancel and return to previous page"
            >
              Cancel and Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
