# 🏢 IoT Smart Building Dashboard

## 📖 Project Story

This project is a special gift from a mentor to his student, **Sister <PERSON><PERSON>**. 

### 🏆 Background & Inspiration

A couple of years ago, I participated in a team competition where we worked on **two IoT-based projects**:

1. **🔒 Laser Security System** - An advanced security solution using laser technology
2. **🦯 Smart Blind Stick** - An assistive technology device for visually impaired individuals

Our team competed at the **12th class level** and achieved something remarkable - we **won 1st position** across the entire **Sindh region** (the whole province) in the **Physics Department** competition!

### 💝 A Gift to My Student

As a **coding enthusiast** and **IoT enthusiast**, I wanted to share my knowledge and passion with the next generation. This Smart Building Dashboard is a small gift to my student, **Sister <PERSON>**, to help her learn modern web development and IoT concepts.

---

## 🚀 Project Overview

This is a **Next.js 14** application featuring a modern IoT dashboard for smart building management. The application includes:

- 🔐 **Authentication System** with login/signup
- 📊 **Real-time Temperature Monitoring** with Chart.js
- 🏠 **Room Management Dashboard**
- ⚙️ **Device Configuration & Threshold Settings**
- 🔒 **Secure Route Protection**
- ♿ **Full Accessibility Support**
- 📱 **Responsive Design**

## 🛠️ Technologies Used

- **Frontend**: Next.js 14 (App Router), React 18, TypeScript
- **Styling**: Tailwind CSS, Radix UI Components
- **Charts**: Chart.js, React-Chartjs-2
- **Authentication**: Custom JWT-like token system
- **Icons**: Lucide React
- **Fonts**: Geist Sans & Mono

## 📁 Project Structure

```
smart-building-frontend/
├── app/                          # Next.js App Router
│   ├── api/auth/                # Authentication API routes
│   ├── dashboard/               # Dashboard pages
│   ├── login/                   # Login page
│   ├── globals.css              # Global styles
│   ├── layout.tsx               # Root layout
│   └── page.tsx                 # Home page (redirects to login)
├── components/                   # React components
│   ├── ui/                      # Reusable UI components
│   ├── dashboard-layout.tsx     # Dashboard layout
│   ├── login-screen.tsx         # Login form
│   ├── navigation.tsx           # Navigation bar
│   ├── room-dashboard.tsx       # Room overview
│   ├── room-sensor-data.tsx     # Sensor data display
│   ├── temperature-graph.tsx    # Chart.js temperature graph
│   ├── set-threshold.tsx        # Threshold configuration
│   └── edit-device.tsx          # Device settings
├── lib/                         # Utility functions
│   ├── auth.ts                  # Authentication utilities
│   └── utils.ts                 # General utilities
└── middleware.ts                # Route protection middleware
```

## 🔧 Installation & Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Step 1: Clone the Repository
```bash
git clone <repository-url>
cd smart-building-frontend-student
```

### Step 2: Install Dependencies
```bash
npm install
# or
yarn install
```

### Step 3: Run Development Server

**⚠️ IMPORTANT: Known Issue with Special Characters in Path**

Sister Ayesha, you might encounter terminal errors due to the `&` symbol in the folder path. This is a common Windows issue, not a problem with your coding skills! Here are multiple solutions:

**🎯 Quick Fix (Recommended):**
```bash
npm run dev-fix
```

**Alternative Methods:**
```bash
# Method 1: Use the custom Node.js script
node start-dev.js

# Method 2: Use alternative npm scripts
npm run dev-safe
# or
npm run dev-alt

# Method 3: Use PowerShell script
.\run-dev.ps1

# Method 4: Use batch file
.\run-dev.bat

# Method 5: Use yarn (if installed)
yarn dev
```

**🔧 Permanent Solution:**
Rename the folder to remove special characters:
```bash
# Rename the folder to remove the & symbol
mv "smart-building-frontend-student-" "smart-building-frontend-student"
cd smart-building-frontend-student
npm run dev
```

**📋 If All Else Fails:**
Check the `TROUBLESHOOTING.md` file for detailed solutions!

### Step 4: Open in Browser
Navigate to [http://localhost:3000](http://localhost:3000)

## 🔑 Demo Credentials

Use these credentials to test the application:

- **Email**: `<EMAIL>`
- **Password**: `password123`

## 🎯 Features

### 🔐 Authentication
- Secure login/signup system
- JWT-like token authentication
- Route protection middleware
- Automatic redirects for unauthorized access

### 📊 Dashboard
- **Room Overview**: Visual grid of all rooms with status indicators
- **Sensor Data**: Real-time temperature, humidity, CO2, and other sensor readings
- **Temperature Analytics**: Interactive Chart.js graphs with 7-day history
- **Threshold Management**: Secure password-protected device configuration
- **Device Settings**: Comprehensive device parameter management

### ♿ Accessibility Features
- WCAG 2.1 compliant
- Full keyboard navigation
- Screen reader support
- ARIA labels and roles
- Focus management
- High contrast support

### 📱 Responsive Design
- Mobile-first approach
- Tablet and desktop optimized
- Touch-friendly interface
- Adaptive layouts

## 🚨 Common Issues & Solutions

### Issue 1: Path with Special Characters
**Error**: `'3dweb\smart-building-frontend-student-\node_modules\.bin\' is not recognized`

**Solutions**:
1. Rename folder to remove `&` character
2. Use full path to node_modules
3. Use yarn instead of npm
4. Run from PowerShell with quotes around paths

### Issue 2: Module Not Found
**Error**: `Cannot find module 'D:\next\dist\bin\next'`

**Solutions**:
1. Delete `node_modules` and `package-lock.json`
2. Run `npm install` again
3. Use `npx next dev` instead of `npm run dev`

### Issue 3: Port Already in Use
**Error**: `Port 3000 is already in use`

**Solution**:
```bash
npm run dev -- -p 3001
# or
npx next dev -p 3001
```

## 🎓 Learning Objectives

This project is designed to teach:

1. **Modern React Development** with Next.js 14 App Router
2. **TypeScript** for type-safe development
3. **Authentication & Security** concepts
4. **Data Visualization** with Chart.js
5. **Accessibility** best practices
6. **Responsive Design** with Tailwind CSS
7. **API Development** with Next.js API routes
8. **State Management** in React
9. **Component Architecture** and reusability
10. **IoT Dashboard** design patterns

## 🤝 Contributing

This is an educational project. Students are encouraged to:

- Add new features
- Improve existing components
- Fix bugs and issues
- Enhance accessibility
- Add more IoT device types
- Implement real backend integration

## 📝 License

This project is created for educational purposes. Feel free to use it for learning and teaching.

---

## 💌 Message to Sister Ayesha

Dear Ayesha,

This dashboard represents the culmination of years of learning in IoT and web development. When we won that competition with our laser security system and smart blind stick, it taught me that technology should serve humanity and make life better.

I hope this project inspires you to:
- **Never stop learning** - Technology evolves rapidly
- **Think about accessibility** - Build for everyone
- **Focus on user experience** - Make technology intuitive
- **Combine creativity with logic** - The best solutions are both functional and beautiful

Remember, every expert was once a beginner. The errors you encounter are not obstacles - they're learning opportunities. Each bug you fix makes you a better developer.

Keep coding, keep creating, and most importantly, keep dreaming big! 🚀

---

**Built with ❤️ for the next generation of IoT developers**

*"The best way to predict the future is to create it." - Peter Drucker*
