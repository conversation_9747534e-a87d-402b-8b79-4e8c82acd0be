"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2 } from "lucide-react"

interface LoginScreenProps {
  onLogin: (email: string, password: string) => Promise<void>
  isLoading?: boolean
}

export default function LoginScreen({ onLogin, isLoading = false }: LoginScreenProps) {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [showSignup, setShowSignup] = useState(false)

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!email || !password) {
      setError("Please fill in all fields")
      return
    }

    try {
      await onLogin(email, password)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Login failed")
    }
  }

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!email || !password) {
      setError("Please fill in all fields")
      return
    }

    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (response.ok) {
        setShowSignup(false)
        setError("")
        // Show success message or auto-login
        alert("Account created successfully! Please log in.")
      } else {
        setError(data.message || 'Signup failed')
      }
    } catch (err) {
      setError("Network error. Please try again.")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-white/95 backdrop-blur-sm shadow-2xl border-0">
        <CardHeader className="text-center pb-2">
          <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            {showSignup ? "Sign Up" : "Login"}
          </CardTitle>
          <p className="text-gray-500 mt-2">
            {showSignup ? "Create your IoT Dashboard account" : "Welcome back to IoT Dashboard"}
          </p>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={showSignup ? handleSignup : handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-gray-700 font-medium">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-gray-50 border-gray-200 focus:border-blue-500 focus:ring-blue-500 h-12"
                placeholder="Enter your email"
                required
                aria-describedby="email-error"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password" className="text-gray-700 font-medium">
                Password
              </Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-gray-50 border-gray-200 focus:border-blue-500 focus:ring-blue-500 h-12"
                placeholder="Enter your password"
                required
                aria-describedby="password-error"
              />
            </div>
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white h-12 font-medium shadow-lg disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {showSignup ? "Creating Account..." : "Logging In..."}
                </>
              ) : (
                showSignup ? "Sign Up" : "Log In"
              )}
            </Button>
          </form>
          <div className="text-center space-y-3">
            <button
              type="button"
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              aria-label="Forgot password"
            >
              Forget Password?
            </button>
            <p className="text-sm text-gray-600">
              {showSignup ? "Already have an account? " : "Don't have an account? "}
              <button
                type="button"
                onClick={() => {
                  setShowSignup(!showSignup)
                  setError("")
                }}
                className="text-purple-600 hover:text-purple-700 font-medium"
                aria-label={showSignup ? "Switch to login" : "Switch to sign up"}
              >
                {showSignup ? "Log in" : "Sign up"}
              </button>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
