"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, CheckCircle } from "lucide-react"

interface DeviceSettings {
  minTemp: string
  maxTemp: string
  description: string
}

interface EditDeviceProps {
  onSave: () => void
}

export default function EditDevice({ onSave }: EditDeviceProps) {
  const [settings, setSettings] = useState<DeviceSettings>({
    minTemp: "10",
    maxTemp: "30",
    description: ""
  })
  const [errors, setErrors] = useState<Partial<DeviceSettings>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")

  const validateSettings = (): boolean => {
    const newErrors: Partial<DeviceSettings> = {}

    // Validate temperature range
    const minTempNum = parseFloat(settings.minTemp)
    const maxTempNum = parseFloat(settings.maxTemp)

    if (isNaN(minTempNum) || minTempNum < -50 || minTempNum > 100) {
      newErrors.minTemp = "Minimum temperature must be between -50°C and 100°C"
    }

    if (isNaN(maxTempNum) || maxTempNum < -50 || maxTempNum > 100) {
      newErrors.maxTemp = "Maximum temperature must be between -50°C and 100°C"
    }

    if (!isNaN(minTempNum) && !isNaN(maxTempNum) && minTempNum >= maxTempNum) {
      newErrors.maxTemp = "Maximum temperature must be greater than minimum temperature"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!validateSettings()) {
      return
    }

    setIsLoading(true)
    setSuccessMessage("")

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setSuccessMessage("Device settings saved successfully!")
      setTimeout(() => {
        onSave()
      }, 1500)
    } catch (error) {
      setErrors({ description: "Failed to save settings. Please try again." })
    } finally {
      setIsLoading(false)
    }
  }

  const updateSetting = (key: keyof DeviceSettings, value: string) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    // Clear error when user starts typing
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: undefined }))
    }
  }

  return (
    <div className="max-w-md mx-auto">
      <Card className="bg-white border-0 shadow-2xl">
        <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-t-lg">
          <CardTitle className="text-xl font-bold flex items-center">⚙️ Set Threshold Values</CardTitle>
          <p className="text-blue-100 text-sm mt-1">Configure device parameters</p>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          {successMessage && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">{successMessage}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-3">
            <Label className="text-gray-700 font-medium flex items-center">
              <span role="img" aria-label="Temperature">🌡️</span> Temperature Range
            </Label>
            <div className="flex items-center space-x-3">
              <div className="flex-1">
                <Label htmlFor="min-temp" className="text-sm text-gray-600 font-medium">
                  Min:
                </Label>
                <Input
                  id="min-temp"
                  type="number"
                  value={settings.minTemp}
                  onChange={(e) => updateSetting('minTemp', e.target.value)}
                  className={`bg-blue-50 border-blue-200 focus:border-blue-500 focus:ring-blue-500 ${
                    errors.minTemp ? 'border-red-500 focus:border-red-500' : ''
                  }`}
                  aria-describedby={errors.minTemp ? "min-temp-error" : undefined}
                  aria-invalid={!!errors.minTemp}
                />
                {errors.minTemp && (
                  <p id="min-temp-error" className="text-red-600 text-xs mt-1" role="alert">
                    {errors.minTemp}
                  </p>
                )}
              </div>
              <div className="flex-1">
                <Label htmlFor="max-temp" className="text-sm text-gray-600 font-medium">
                  Max:
                </Label>
                <Input
                  id="max-temp"
                  type="number"
                  value={settings.maxTemp}
                  onChange={(e) => updateSetting('maxTemp', e.target.value)}
                  className={`bg-blue-50 border-blue-200 focus:border-blue-500 focus:ring-blue-500 ${
                    errors.maxTemp ? 'border-red-500 focus:border-red-500' : ''
                  }`}
                  aria-describedby={errors.maxTemp ? "max-temp-error" : undefined}
                  aria-invalid={!!errors.maxTemp}
                />
                {errors.maxTemp && (
                  <p id="max-temp-error" className="text-red-600 text-xs mt-1" role="alert">
                    {errors.maxTemp}
                  </p>
                )}
              </div>
              <span className="text-sm text-gray-600 font-medium bg-gray-100 px-2 py-1 rounded">°C</span>
            </div>
          </div>

          <div className="flex items-center justify-center">
            <Badge className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 shadow-md">
              <span role="img" aria-label="Alarm">🚨</span> Alarm Set & Active
            </Badge>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-gray-700 font-medium flex items-center">
              <span role="img" aria-label="Description">📝</span> Description
            </Label>
            <Textarea
              id="description"
              value={settings.description}
              onChange={(e) => updateSetting('description', e.target.value)}
              className="bg-gray-50 border-gray-200 focus:border-purple-500 focus:ring-purple-500 min-h-[80px]"
              placeholder="Enter threshold description and notes..."
              aria-describedby="description-help"
            />
            <p id="description-help" className="text-xs text-gray-500">
              Optional: Add notes about this threshold configuration
            </p>
          </div>

          {errors.description && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.description}</AlertDescription>
            </Alert>
          )}

          <Button
            onClick={handleSave}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white h-12 font-medium shadow-lg disabled:opacity-50 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
            aria-label="Save device threshold settings"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <span role="img" aria-label="Save">💾</span> Save Changes
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
