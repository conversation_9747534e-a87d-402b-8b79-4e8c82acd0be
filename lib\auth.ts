// Authentication utilities for client-side

export const AUTH_TOKEN_KEY = 'auth-token'

export function getAuthToken(): string | null {
  if (typeof window === 'undefined') {
    return null // Server-side
  }
  return localStorage.getItem(AUTH_TOKEN_KEY)
}

export function setAuthToken(token: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(AUTH_TOKEN_KEY, token)
  }
}

export function removeAuthToken(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(AUTH_TOKEN_KEY)
  }
}

export function isAuthenticated(): boolean {
  return getAuthToken() !== null
}

// Mock token validation (in a real app, you'd validate with your backend)
export function isValidToken(token: string): boolean {
  // Simple check for mock tokens
  return token.startsWith('mock-jwt-token-')
}

export async function validateAuthToken(): Promise<boolean> {
  const token = getAuthToken()
  if (!token) {
    return false
  }
  
  // In a real application, you would make an API call to validate the token
  // For now, we'll just check if it's a valid mock token
  return isValidToken(token)
}
