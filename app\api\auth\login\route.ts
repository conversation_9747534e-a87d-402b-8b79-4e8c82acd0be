import { NextRequest, NextResponse } from 'next/server'

// Dummy credentials for testing
const DUMMY_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'password123'
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Check credentials
    if (email === DUMMY_CREDENTIALS.email && password === DUMMY_CREDENTIALS.password) {
      // Generate a mock JWT token (in a real app, use proper JWT library)
      const mockToken = `mock-jwt-token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      return NextResponse.json(
        { 
          message: 'Login successful',
          token: mockToken,
          user: {
            email: email,
            name: 'Test User'
          }
        },
        { status: 200 }
      )
    } else {
      return NextResponse.json(
        { message: 'Invalid email or password' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    { status: 405 }
  )
}
