import type { Metada<PERSON> } from 'next'
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import './globals.css'

export const metadata: Metadata = {
  title: 'IoT Smart Building Dashboard',
  description: 'Monitor and control your smart building IoT devices with real-time analytics and threshold management.',
  keywords: ['IoT', 'Smart Building', 'Dashboard', 'Temperature', 'Sensors'],
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${GeistSans.variable} ${GeistMono.variable}`}>
      <body className="font-sans antialiased">{children}</body>
    </html>
  )
}
