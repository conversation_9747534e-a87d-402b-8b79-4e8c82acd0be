#!/usr/bin/env node

/**
 * IoT Smart Building Dashboard - Development Server Starter
 * This script helps bypass path issues with special characters
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🏢 IoT Smart Building Dashboard');
console.log('=====================================');
console.log('Starting development server...\n');

// Check if node_modules exists
if (!fs.existsSync('node_modules')) {
    console.log('❌ node_modules not found. Installing dependencies...');
    const install = spawn('npm', ['install'], { stdio: 'inherit', shell: true });
    
    install.on('close', (code) => {
        if (code === 0) {
            console.log('✅ Dependencies installed successfully!');
            startDevServer();
        } else {
            console.log('❌ Failed to install dependencies');
            process.exit(1);
        }
    });
} else {
    startDevServer();
}

function startDevServer() {
    console.log('🚀 Starting Next.js development server...\n');
    
    // Try different methods to start the server
    const methods = [
        { name: 'next dev', cmd: 'npx', args: ['next', 'dev'] },
        { name: 'direct next', cmd: 'node', args: [path.join('node_modules', 'next', 'dist', 'bin', 'next'), 'dev'] },
        { name: 'npm run dev', cmd: 'npm', args: ['run', 'dev'] }
    ];
    
    let currentMethod = 0;
    
    function tryNextMethod() {
        if (currentMethod >= methods.length) {
            console.log('\n❌ All methods failed!');
            console.log('\n🔧 Troubleshooting suggestions:');
            console.log('1. Rename folder to remove special characters (&)');
            console.log('2. Move project to a path without special characters');
            console.log('3. Try running: yarn dev');
            console.log('4. Check TROUBLESHOOTING.md for more solutions');
            return;
        }
        
        const method = methods[currentMethod];
        console.log(`📝 Trying method ${currentMethod + 1}: ${method.name}`);
        
        const child = spawn(method.cmd, method.args, { 
            stdio: 'inherit', 
            shell: true,
            cwd: process.cwd()
        });
        
        child.on('error', (error) => {
            console.log(`❌ Method ${currentMethod + 1} failed: ${error.message}`);
            currentMethod++;
            setTimeout(tryNextMethod, 1000);
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                console.log('✅ Development server started successfully!');
            } else {
                console.log(`❌ Method ${currentMethod + 1} failed with code ${code}`);
                currentMethod++;
                setTimeout(tryNextMethod, 1000);
            }
        });
        
        // If the process is still running after 5 seconds, assume it's successful
        setTimeout(() => {
            if (!child.killed) {
                console.log('✅ Development server appears to be running!');
                console.log('🌐 Open http://localhost:3000 in your browser');
                console.log('🔑 Login with: <EMAIL> / password123');
            }
        }, 5000);
    }
    
    tryNextMethod();
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down development server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 Shutting down development server...');
    process.exit(0);
});
