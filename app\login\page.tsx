"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import LoginScreen from "@/components/login-screen"
import { setAuthToken } from "@/lib/auth"

export default function LoginPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleLogin = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (response.ok) {
        // Store token using auth utility
        setAuthToken(data.token)
        router.push('/dashboard')
      } else {
        throw new Error(data.message || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error // Re-throw to let LoginScreen handle the error display
    } finally {
      setIsLoading(false)
    }
  }

  return <LoginScreen onLogin={handleLogin} isLoading={isLoading} />
}
